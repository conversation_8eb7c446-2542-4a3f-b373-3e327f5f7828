import { ApiProperty } from '@nestjs/swagger';

export class SkippedRecordDto {
  @ApiProperty()
  name: string;

  @ApiProperty()
  reason: string;

  @ApiProperty({ required: false })
  rowIndex?: number;
}

export class BulkUploadResponseDto {
  @ApiProperty()
  totalRecords: number;

  @ApiProperty()
  uploadedRecords: number;

  @ApiProperty()
  skippedRecords: number;

  @ApiProperty()
  pendingAuthorization: number;

  @ApiProperty({ type: [SkippedRecordDto] })
  skippedDetails: SkippedRecordDto[];

  @ApiProperty()
  message: string;
}
